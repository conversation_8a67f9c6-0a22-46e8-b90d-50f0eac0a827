﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Model;
using Services;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.OData.Query.Validator;
using System.Net;
using Helper;

namespace PRN231_SU25_SE170115.api.Controllers
{
    [ApiController]
    [Route("api/handbags")]
    [Authorize]
    public class HandbagsController : ControllerBase
    {
        private readonly HangBagService _handbagService;

        public HandbagsController(HangBagService handbagService)
        {
            _handbagService = handbagService;
        }

        [HttpGet]
        [Authorize(Roles = "administrator,moderator,developer,member")]
        public IActionResult GetAllHandbags()
        {
            var handbags = _handbagService.GetAllHandBag();
            return Ok(handbags);
        }

        [HttpGet("{id}")]
        [Authorize(Roles = "administrator,moderator,developer,member")]
        public IActionResult GetHandbagById(int id)
        {
            var handbag = _handbagService.GetHandBagById(id);
            if (handbag == null)
                return ErrorResultHelper.Create(HttpStatusCode.NotFound);

            return Ok(handbag);
        }

        [HttpPost]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult CreateHandbag([FromBody] CreateHandBagModel model)
        {
            if (!ModelState.IsValid)
                return ErrorResultHelper.Create(HttpStatusCode.BadRequest);

            var regex = new Regex(@"^([A-Z0-9][a-zA-Z0-9#]*\s)*([A-Z0-9][a-zA-Z0-9#]*)$");
            if (!regex.IsMatch(model.ModelName))
                return ErrorResultHelper.Create(HttpStatusCode.BadRequest);

            if (model.Price <= 0 || model.Stock <= 0)
                return ErrorResultHelper.Create(HttpStatusCode.BadRequest);

            var result = _handbagService.CreateHandBag(model);
            if (result== null)
                return ErrorResultHelper.Create(HttpStatusCode.BadRequest);

            return StatusCode(201, new { message = "Created successfully", item = result });
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult UpdateHandbag(int id, [FromBody] UpdateHandBagModel model)
        {
            if (id <= 0)
                return ErrorResultHelper.Create(HttpStatusCode.BadRequest);

            var item = _handbagService.GetHandBagById(id);
            if (item == null)
                return ErrorResultHelper.Create(HttpStatusCode.NotFound);

            if (!ModelState.IsValid)
                return ErrorResultHelper.Create(HttpStatusCode.BadRequest);

            var regex = new Regex(@"^([A-Z0-9][a-zA-Z0-9#]*\s)*([A-Z0-9][a-zA-Z0-9#]*)$");
            if (!regex.IsMatch(model.ModelName))
                return ErrorResultHelper.Create(HttpStatusCode.BadRequest);

            if (model.Price <= 0 || model.Stock <= 0)
                return ErrorResultHelper.Create(HttpStatusCode.BadRequest);

            var result = _handbagService.UpdateHandBag(id, model);
            if (!result)
                return ErrorResultHelper.Create(HttpStatusCode.BadRequest);

            return Ok(new { message = "Updated successfully" });
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult DeleteHandbag(int id)
        {
            if (id <= 0)
                return ErrorResultHelper.Create(HttpStatusCode.BadRequest);

            var result = _handbagService.DeleteHandBag(id);
            if (!result)
                return ErrorResultHelper.Create(HttpStatusCode.NotFound);

            return Ok(new { message = "Deleted successfully" });
        }

        [Authorize(Roles = "administrator,moderator,developer,member")]
        [HttpGet("search")]
        public IActionResult SearchHandbags([FromServices] ODataQueryOptions<ListHandBagModel> odataOptions, [FromQuery] string? modelName, [FromQuery] string? material)
        {
            var query = _handbagService.SearchWithProjection(modelName, material);

            var settings = new ODataValidationSettings();

            try
            {
                odataOptions.Validate(settings);

                var results = (IQueryable<ListHandBagModel>)odataOptions.ApplyTo(query);
                var filtered = results.ToList();

                var grouped = filtered
                    .GroupBy(h => h.BrandName)
                    .Select(g => new GroupedHandbagModel
                    {
                        BrandName = g.Key,
                        Handbags = g.ToList()
                    });

                return Ok(grouped);
            }
            catch (Exception)
            {
                return ErrorResultHelper.Create(HttpStatusCode.InternalServerError);
            }
        }
    }
}
